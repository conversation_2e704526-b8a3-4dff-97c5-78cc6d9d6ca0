import 'dart:io';
import 'dart:async';
import 'package:path_provider/path_provider.dart';
import 'logger_service.dart';
import 'file_hash_service.dart';

/// 存储空间清理服务
/// 监控存储空间使用率，当达到70%时自动清理最早的5天文件
class StorageCleanupService {
  static final StorageCleanupService _instance =
      StorageCleanupService._internal();
  factory StorageCleanupService() => _instance;
  StorageCleanupService._internal();

  Timer? _monitorTimer;
  bool _isMonitoring = false;
  bool _isCleaningUp = false;

  // 文件hash服务
  final FileHashService _fileHashService = FileHashService();

  // 存储使用率阈值 (70%)
  static const double _storageThreshold = 0.7;

  // 清理天数
  static const int _cleanupDays = 5;

  // 监控间隔 (每5分钟检查一次)
  static const Duration _monitorInterval = Duration(minutes: 5);

  /// 开始监控存储空间
  Future<void> startMonitoring() async {
    if (_isMonitoring) return;

    _isMonitoring = true;
    logger.i('开始监控存储空间使用率');

    // 立即检查一次
    await _checkStorageAndCleanup();

    // 定时检查
    _monitorTimer = Timer.periodic(_monitorInterval, (timer) async {
      await _checkStorageAndCleanup();
    });
  }

  /// 停止监控存储空间
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    _monitorTimer?.cancel();
    _monitorTimer = null;
    logger.i('停止监控存储空间使用率');
  }

  /// 检查存储空间并执行清理
  Future<void> _checkStorageAndCleanup() async {
    if (_isCleaningUp) return;

    try {
      final storageUsage = await getStorageUsage();
      logger.i('当前存储使用率: ${(storageUsage * 100).toStringAsFixed(1)}%');

      if (storageUsage >= _storageThreshold) {
        logger.w('存储使用率达到${(storageUsage * 100).toStringAsFixed(1)}%，开始自动清理');
        await performCleanup();
      }
    } catch (e) {
      logger.e('检查存储空间时出错: $e');
    }
  }

  /// 获取存储使用率
  Future<double> getStorageUsage() async {
    try {
      final directory = await getApplicationDocumentsDirectory();

      // 获取可用空间和总空间
      final freeSpace = await _getAvailableSpace(directory.path);
      final totalSpace = await _getTotalSpace(directory.path);

      if (totalSpace > 0) {
        final usedSpace = totalSpace - freeSpace;
        return usedSpace / totalSpace;
      }

      return 0.0;
    } catch (e) {
      logger.e('获取存储使用率失败: $e');
      return 0.0;
    }
  }

  /// 执行清理操作
  Future<void> performCleanup() async {
    if (_isCleaningUp) return;

    _isCleaningUp = true;

    try {
      logger.i('开始清理最早$_cleanupDays天的文件');

      final directory = await getApplicationDocumentsDirectory();
      final cutoffDate = DateTime.now().subtract(Duration(days: _cleanupDays));

      int deletedFiles = 0;
      int deletedSize = 0;

      await _cleanupDirectory(directory, cutoffDate, (count, size) {
        deletedFiles += count;
        deletedSize += size;
      });

      logger.i(
        '清理完成: 删除了 $deletedFiles 个文件，释放了 ${_formatBytes(deletedSize)} 空间',
      );

      // 清理无效的hash记录
      await _fileHashService.cleanupInvalidHashes();
    } catch (e) {
      logger.e('清理文件时出错: $e');
    } finally {
      _isCleaningUp = false;
    }
  }

  /// 递归清理目录中的旧文件
  Future<void> _cleanupDirectory(
    Directory directory,
    DateTime cutoffDate,
    Function(int count, int size) onProgress,
  ) async {
    try {
      final entities = directory.listSync(recursive: false);

      for (final entity in entities) {
        if (entity is File) {
          final stat = await entity.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            final size = stat.size;
            await entity.delete();

            // 同时删除hash记录
            await _fileHashService.removeFileHashByPath(entity.path);

            onProgress(1, size);
            logger.d('删除文件: ${entity.path}');
          }
        } else if (entity is Directory) {
          // 递归清理子目录
          await _cleanupDirectory(entity, cutoffDate, onProgress);

          // 如果目录为空，删除目录
          if (await _isDirectoryEmpty(entity)) {
            await entity.delete();
            logger.d('删除空目录: ${entity.path}');
          }
        }
      }
    } catch (e) {
      logger.e('清理目录 ${directory.path} 时出错: $e');
    }
  }

  /// 检查目录是否为空
  Future<bool> _isDirectoryEmpty(Directory directory) async {
    try {
      final entities = await directory.list().toList();
      return entities.isEmpty;
    } catch (e) {
      return false;
    }
  }

  /// 获取可用空间 (字节)
  Future<int> _getAvailableSpace(String path) async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        // 移动平台使用 StatFs
        final result = await Process.run('df', [path]);
        if (result.exitCode == 0) {
          final lines = result.stdout.toString().split('\n');
          if (lines.length > 1) {
            final parts = lines[1].split(RegExp(r'\s+'));
            if (parts.length > 3) {
              return int.tryParse(parts[3]) ?? 0;
            }
          }
        }
      } else {
        // 桌面平台
        final result = await Process.run('df', ['-B1', path]);
        if (result.exitCode == 0) {
          final lines = result.stdout.toString().split('\n');
          if (lines.length > 1) {
            final parts = lines[1].split(RegExp(r'\s+'));
            if (parts.length > 3) {
              return int.tryParse(parts[3]) ?? 0;
            }
          }
        }
      }

      return 0;
    } catch (e) {
      logger.e('获取可用空间失败: $e');
      return 0;
    }
  }

  /// 获取总空间 (字节)
  Future<int> _getTotalSpace(String path) async {
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        final result = await Process.run('df', [path]);
        if (result.exitCode == 0) {
          final lines = result.stdout.toString().split('\n');
          if (lines.length > 1) {
            final parts = lines[1].split(RegExp(r'\s+'));
            if (parts.length > 1) {
              return int.tryParse(parts[1]) ?? 0;
            }
          }
        }
      } else {
        final result = await Process.run('df', ['-B1', path]);
        if (result.exitCode == 0) {
          final lines = result.stdout.toString().split('\n');
          if (lines.length > 1) {
            final parts = lines[1].split(RegExp(r'\s+'));
            if (parts.length > 1) {
              return int.tryParse(parts[1]) ?? 0;
            }
          }
        }
      }

      return 0;
    } catch (e) {
      logger.e('获取总空间失败: $e');
      return 0;
    }
  }

  /// 格式化字节数为可读格式
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// 手动触发清理
  Future<void> manualCleanup() async {
    logger.i('手动触发存储清理');
    await performCleanup();
    // 清理无效的hash记录
    await _fileHashService.cleanupInvalidHashes();
  }

  /// 获取存储信息
  Future<Map<String, dynamic>> getStorageInfo() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final freeSpace = await _getAvailableSpace(directory.path);
      final totalSpace = await _getTotalSpace(directory.path);
      final usedSpace = totalSpace - freeSpace;
      final usageRate = totalSpace > 0 ? usedSpace / totalSpace : 0.0;

      return {
        'totalSpace': totalSpace,
        'usedSpace': usedSpace,
        'freeSpace': freeSpace,
        'usageRate': usageRate,
        'formattedTotal': _formatBytes(totalSpace),
        'formattedUsed': _formatBytes(usedSpace),
        'formattedFree': _formatBytes(freeSpace),
        'usagePercentage': '${(usageRate * 100).toStringAsFixed(1)}%',
      };
    } catch (e) {
      logger.e('获取存储信息失败: $e');
      return {};
    }
  }

  /// 释放资源
  void dispose() {
    stopMonitoring();
  }
}
